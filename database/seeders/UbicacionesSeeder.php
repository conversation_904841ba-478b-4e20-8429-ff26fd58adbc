<?php

namespace Database\Seeders;

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;
use Illuminate\Database\Seeder;

class LocationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // All 24 Venezuelan states
        $states = [
            ['name' => 'Amazonas', 'code' => 'AM'],
            ['name' => 'Anzoátegui', 'code' => 'AN'],
            ['name' => 'Apure', 'code' => 'AP'],
            ['name' => 'Aragua', 'code' => 'AR'],
            ['name' => 'Barinas', 'code' => 'BA'],
            ['name' => 'Bolívar', 'code' => 'BO'],
            ['name' => 'Carabobo', 'code' => 'CA'],
            ['name' => 'Cojedes', 'code' => 'CO'],
            ['name' => 'Delta Amacuro', 'code' => 'DA'],
            ['name' => 'Distrito Capital', 'code' => 'DC'],
            ['name' => 'Falcón', 'code' => 'FA'],
            ['name' => 'Guárico', 'code' => 'GU'],
            ['name' => 'La Guaira', 'code' => 'LG'],
            ['name' => 'Lara', 'code' => 'LA'],
            ['name' => 'Mérida', 'code' => 'ME'],
            ['name' => 'Miranda', 'code' => 'MI'],
            ['name' => 'Monagas', 'code' => 'MO'],
            ['name' => 'Nueva Esparta', 'code' => 'NE'],
            ['name' => 'Portuguesa', 'code' => 'PO'],
            ['name' => 'Sucre', 'code' => 'SU'],
            ['name' => 'Táchira', 'code' => 'TA'],
            ['name' => 'Trujillo', 'code' => 'TR'],
            ['name' => 'Yaracuy', 'code' => 'YA'],
            ['name' => 'Zulia', 'code' => 'ZU'],
        ];

        foreach ($states as $stateData) {
            $state = State::create($stateData);

            // Create municipalities for each state
            $municipalities = $this->getMunicipalitiesByState($state->code);

            foreach ($municipalities as $municipalityData) {
                $municipality = $state->municipalities()->create($municipalityData);

                // Create parishes for each municipality
                $parishes = $this->getParishesByMunicipality($municipality->code);

                foreach ($parishes as $parishData) {
                    $parish = $municipality->parishes()->create($parishData);

                    // Create voting centers for each parish
                    $centers = $this->getCentersByParish($parish->code);

                    foreach ($centers as $centerData) {
                        $parish->votingCenters()->create($centerData);
                    }
                }
            }
        }
    }

    private function getMunicipalitiesByState(string $stateCode): array
    {
        $municipalities = [
            // Amazonas
            'AM' => [
                ['name' => 'Alto Orinoco', 'code' => 'ALO'],
                ['name' => 'Atabapo', 'code' => 'ATA'],
                ['name' => 'Atures', 'code' => 'ATU'],
                ['name' => 'Autana', 'code' => 'AUT'],
                ['name' => 'Manapiare', 'code' => 'MAN'],
                ['name' => 'Maroa', 'code' => 'MAR'],
                ['name' => 'Río Negro', 'code' => 'RNE'],
            ],
            // Anzoátegui
            'AN' => [
                ['name' => 'Anaco', 'code' => 'ANA'],
                ['name' => 'Aragua', 'code' => 'ARA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Bruzual', 'code' => 'BRU'],
                ['name' => 'Cajigal', 'code' => 'CAJ'],
                ['name' => 'Carvajal', 'code' => 'CAR'],
                ['name' => 'Freites', 'code' => 'FRE'],
                ['name' => 'Guanipa', 'code' => 'GUA'],
                ['name' => 'Guanta', 'code' => 'GAN'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Sir Arthur McGregor', 'code' => 'SAM'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monagas', 'code' => 'MON'],
                ['name' => 'Peñalver', 'code' => 'PEN'],
                ['name' => 'Píritu', 'code' => 'PIR'],
                ['name' => 'San Juan de Capistrano', 'code' => 'SJC'],
                ['name' => 'Santa Ana', 'code' => 'SAN'],
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Sotillo', 'code' => 'SOT'],
                ['name' => 'Turístico Diego Bautista Urbaneja', 'code' => 'TDB'],
            ],
            // Apure
            'AP' => [
                ['name' => 'Achaguas', 'code' => 'ACH'],
                ['name' => 'Biruaca', 'code' => 'BIR'],
                ['name' => 'Pedro Camejo', 'code' => 'PCM'],
                ['name' => 'Muñoz', 'code' => 'MUN'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
                ['name' => 'San Fernando', 'code' => 'SFE'],
            ],
            // Aragua
            'AR' => [
                ['name' => 'Alcántara', 'code' => 'ALC'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Camatagua', 'code' => 'CAM'],
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Iragorry', 'code' => 'IRA'],
                ['name' => 'Lamas', 'code' => 'LAM'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Mariño', 'code' => 'MAR'],
                ['name' => 'Ocumare de la Costa de Oro', 'code' => 'OCU'],
                ['name' => 'Revenga', 'code' => 'REV'],
                ['name' => 'Ribas', 'code' => 'RIB'],
                ['name' => 'San Casimiro', 'code' => 'SCA'],
                ['name' => 'San Sebastián', 'code' => 'SSE'],
                ['name' => 'Santiago Mariño', 'code' => 'SMA'],
                ['name' => 'Santos Michelena', 'code' => 'SMI'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tovar', 'code' => 'TOV'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
            ],
            // Barinas
            'BA' => [
                ['name' => 'Alberto Torrealba', 'code' => 'ATO'],
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Antonio José de Sucre', 'code' => 'AJS'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'Barinas', 'code' => 'BAR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cruz Paredes', 'code' => 'CPA'],
                ['name' => 'Ezequiel Zamora', 'code' => 'EZA'],
                ['name' => 'Obispos', 'code' => 'OBI'],
                ['name' => 'Pedraza', 'code' => 'PED'],
                ['name' => 'Rojas', 'code' => 'ROJ'],
                ['name' => 'Sosa', 'code' => 'SOS'],
            ],
            // Bolívar
            'BO' => [
                ['name' => 'Angostura', 'code' => 'ANG'],
                ['name' => 'Caroní', 'code' => 'CAR'],
                ['name' => 'Cedeño', 'code' => 'CED'],
                ['name' => 'El Callao', 'code' => 'ECA'],
                ['name' => 'Gran Sabana', 'code' => 'GSA'],
                ['name' => 'Heres', 'code' => 'HER'],
                ['name' => 'Piar', 'code' => 'PIA'],
                ['name' => 'Roscio', 'code' => 'ROS'],
                ['name' => 'Sifontes', 'code' => 'SIF'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Padre Pedro Chien', 'code' => 'PPC'],
            ],
            // Carabobo
            'CA' => [
                ['name' => 'Bejuma', 'code' => 'BEJ'],
                ['name' => 'Carlos Arvelo', 'code' => 'CAR'],
                ['name' => 'Diego Ibarra', 'code' => 'DIB'],
                ['name' => 'Guacara', 'code' => 'GUA'],
                ['name' => 'Juan José Mora', 'code' => 'JJM'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Los Guayos', 'code' => 'LGU'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Montalbán', 'code' => 'MON'],
                ['name' => 'Naguanagua', 'code' => 'NAG'],
                ['name' => 'Puerto Cabello', 'code' => 'PCA'],
                ['name' => 'San Diego', 'code' => 'SDI'],
                ['name' => 'San Joaquín', 'code' => 'SJO'],
                ['name' => 'Valencia', 'code' => 'VAL'],
            ],
            // Cojedes
            'CO' => [
                ['name' => 'Anzoátegui', 'code' => 'ANZ'],
                ['name' => 'Falcón', 'code' => 'FAL'],
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Lima Blanco', 'code' => 'LBL'],
                ['name' => 'Pao de San Juan Bautista', 'code' => 'PSJ'],
                ['name' => 'Ricaurte', 'code' => 'RIC'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
                ['name' => 'San Carlos', 'code' => 'SCA'],
                ['name' => 'Tinaco', 'code' => 'TIN'],
            ],
            // Delta Amacuro
            'DA' => [
                ['name' => 'Antonio Díaz', 'code' => 'ADI'],
                ['name' => 'Casacoima', 'code' => 'CAS'],
                ['name' => 'Pedernales', 'code' => 'PED'],
                ['name' => 'Tucupita', 'code' => 'TUC'],
            ],
            // Distrito Capital
            'DC' => [
                ['name' => 'Libertador', 'code' => 'LIB'],
            ],
        ];

        return $municipalities[$stateCode] ?? [];
    }

    private function getParishesByMunicipality(string $municipalityCode): array
    {
        $parishes = [
            'LIB' => [
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'San Juan', 'code' => 'SJU'],
                ['name' => 'Santa Teresa', 'code' => 'STE'],
            ],
            'BAR' => [
                ['name' => 'Baruta', 'code' => 'BAR'],
                ['name' => 'El Cafetal', 'code' => 'CAF'],
            ],
            'CHA' => [
                ['name' => 'Chacao', 'code' => 'CHA'],
            ],
            'HAT' => [
                ['name' => 'El Hatillo', 'code' => 'HAT'],
            ],
            'SUC' => [
                ['name' => 'Petare', 'code' => 'PET'],
                ['name' => 'Caucagüita', 'code' => 'CAU'],
            ],
            'VAL' => [
                ['name' => 'Valencia', 'code' => 'VAL'],
                ['name' => 'Miguel Peña', 'code' => 'MPE'],
            ],
            'PCA' => [
                ['name' => 'Puerto Cabello', 'code' => 'PCA'],
            ],
            'MAR' => [
                ['name' => 'Maracaibo', 'code' => 'MAR'],
                ['name' => 'Cacique Mara', 'code' => 'CMA'],
            ],
            'SFR' => [
                ['name' => 'San Francisco', 'code' => 'SFR'],
            ],
            'GIR' => [
                ['name' => 'Maracay', 'code' => 'MAC'],
                ['name' => 'San Jacinto', 'code' => 'SJA'],
            ],
        ];

        return $parishes[$municipalityCode] ?? [];
    }

    private function getCentersByParish(string $parishCode): array
    {
        return [
            [
                'name' => 'Basic School ' . $parishCode . ' 001',
                'code' => $parishCode . '001',
                'address' => 'Example address for center ' . $parishCode . '001',
            ],
            [
                'name' => 'High School ' . $parishCode . ' 002',
                'code' => $parishCode . '002',
                'address' => 'Example address for center ' . $parishCode . '002',
            ],
        ];
    }
}
