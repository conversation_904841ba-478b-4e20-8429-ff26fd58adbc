<?php

namespace Database\Seeders;

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;
use Illuminate\Database\Seeder;

class LocationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some example states (Venezuela)
        $states = [
            ['name' => 'Distrito Capital', 'code' => 'DC'],
            ['name' => 'Miranda', 'code' => 'MI'],
            ['name' => 'Carabobo', 'code' => 'CA'],
            ['name' => 'Zulia', 'code' => 'ZU'],
            ['name' => 'Aragua', 'code' => 'AR'],
        ];

        foreach ($states as $stateData) {
            $state = State::create($stateData);

            // Create municipalities for each state
            $municipalities = $this->getMunicipalitiesByState($state->code);

            foreach ($municipalities as $municipalityData) {
                $municipality = $state->municipalities()->create($municipalityData);

                // Create parishes for each municipality
                $parishes = $this->getParishesByMunicipality($municipality->code);

                foreach ($parishes as $parishData) {
                    $parish = $municipality->parishes()->create($parishData);

                    // Create voting centers for each parish
                    $centers = $this->getCentersByParish($parish->code);

                    foreach ($centers as $centerData) {
                        $parish->votingCenters()->create($centerData);
                    }
                }
            }
        }
    }

    private function getMunicipalitiesByState(string $stateCode): array
    {
        $municipalities = [
            'DC' => [
                ['name' => 'Libertador', 'code' => 'LIB'],
            ],
            'MI' => [
                ['name' => 'Baruta', 'code' => 'BAR'],
                ['name' => 'Chacao', 'code' => 'CHA'],
                ['name' => 'El Hatillo', 'code' => 'HAT'],
                ['name' => 'Sucre', 'code' => 'SUC'],
            ],
            'CA' => [
                ['name' => 'Valencia', 'code' => 'VAL'],
                ['name' => 'Puerto Cabello', 'code' => 'PCA'],
            ],
            'ZU' => [
                ['name' => 'Maracaibo', 'code' => 'MAR'],
                ['name' => 'San Francisco', 'code' => 'SFR'],
            ],
            'AR' => [
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Libertador', 'code' => 'LIB'],
            ],
        ];

        return $municipalities[$stateCode] ?? [];
    }

    private function getParishesByMunicipality(string $municipalityCode): array
    {
        $parishes = [
            'LIB' => [
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'San Juan', 'code' => 'SJU'],
                ['name' => 'Santa Teresa', 'code' => 'STE'],
            ],
            'BAR' => [
                ['name' => 'Baruta', 'code' => 'BAR'],
                ['name' => 'El Cafetal', 'code' => 'CAF'],
            ],
            'CHA' => [
                ['name' => 'Chacao', 'code' => 'CHA'],
            ],
            'HAT' => [
                ['name' => 'El Hatillo', 'code' => 'HAT'],
            ],
            'SUC' => [
                ['name' => 'Petare', 'code' => 'PET'],
                ['name' => 'Caucagüita', 'code' => 'CAU'],
            ],
            'VAL' => [
                ['name' => 'Valencia', 'code' => 'VAL'],
                ['name' => 'Miguel Peña', 'code' => 'MPE'],
            ],
            'PCA' => [
                ['name' => 'Puerto Cabello', 'code' => 'PCA'],
            ],
            'MAR' => [
                ['name' => 'Maracaibo', 'code' => 'MAR'],
                ['name' => 'Cacique Mara', 'code' => 'CMA'],
            ],
            'SFR' => [
                ['name' => 'San Francisco', 'code' => 'SFR'],
            ],
            'GIR' => [
                ['name' => 'Maracay', 'code' => 'MAC'],
                ['name' => 'San Jacinto', 'code' => 'SJA'],
            ],
        ];

        return $parishes[$municipalityCode] ?? [];
    }

    private function getCentersByParish(string $parishCode): array
    {
        return [
            [
                'name' => 'Basic School ' . $parishCode . ' 001',
                'code' => $parishCode . '001',
                'address' => 'Example address for center ' . $parishCode . '001',
            ],
            [
                'name' => 'High School ' . $parishCode . ' 002',
                'code' => $parishCode . '002',
                'address' => 'Example address for center ' . $parishCode . '002',
            ],
        ];
    }
}
